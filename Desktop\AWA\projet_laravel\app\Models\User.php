<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'phone',
        'date_of_birth',
        'bio',
        'avatar_url',
        'city',
        'country',
        'eco_score',
        'is_verified',
        'is_active',
        'role',
        'preferences',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'date_of_birth' => 'date',
        'eco_score' => 'integer',
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
        'preferences' => 'array',
    ];

    /**
     * User roles constants
     */
    const ROLE_USER = 'user';
    const ROLE_ORGANIZER = 'organizer';
    const ROLE_ADMIN = 'admin';

    /**
     * Get all available roles
     */
    public static function getRoles(): array
    {
        return [
            self::ROLE_USER,
            self::ROLE_ORGANIZER,
            self::ROLE_ADMIN,
        ];
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole(self::ROLE_ADMIN);
    }

    /**
     * Check if user is organizer
     */
    public function isOrganizer(): bool
    {
        return $this->hasRole(self::ROLE_ORGANIZER);
    }

    /**
     * Get user's full name
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get user's initials
     */
    public function getInitialsAttribute(): string
    {
        $first = $this->first_name ? strtoupper(substr($this->first_name, 0, 1)) : '';
        $last = $this->last_name ? strtoupper(substr($this->last_name, 0, 1)) : '';
        return $first . $last;
    }

    /**
     * Get avatar URL or default
     */
    public function getAvatarUrlAttribute($value): string
    {
        if ($value) {
            return $value;
        }
        
        // Generate avatar from initials using a service like Gravatar or UI Avatars
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->full_name) . '&background=random';
    }

    /**
     * Add eco score points
     */
    public function addEcoScore(int $points): void
    {
        $this->increment('eco_score', $points);
    }

    /**
     * Get user's location as string
     */
    public function getLocationAttribute(): string
    {
        $location = [];
        if ($this->city) $location[] = $this->city;
        if ($this->country) $location[] = $this->country;
        return implode(', ', $location);
    }
}
