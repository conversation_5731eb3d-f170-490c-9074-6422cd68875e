<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            
            // Basic info
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();
            
            // Contact info
            $table->string('phone')->nullable();
            $table->date('date_of_birth')->nullable();
            
            // Profile info
            $table->text('bio')->nullable();
            $table->string('avatar_url')->nullable();
            
            // Location
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            
            // EcoEvents specific
            $table->integer('eco_score')->default(0);
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_active')->default(true);
            $table->enum('role', ['user', 'organizer', 'admin'])->default('user');
            
            // Preferences (JSON)
            $table->json('preferences')->nullable();
            
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['role', 'is_active']);
            $table->index('eco_score');
            $table->index(['city', 'country']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
