<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Non authentifié'
            ], 401);
        }

        $user = auth()->user();

        // Check if user is verified
        if (!$user->is_verified) {
            return response()->json([
                'success' => false,
                'message' => 'Compte non vérifié. Veuillez vérifier votre email.',
                'user_id' => $user->id,
                'email' => $user->email
            ], 403);
        }

        return $next($request);
    }
}

