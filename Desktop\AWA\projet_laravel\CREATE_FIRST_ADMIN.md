# 👑 <PERSON><PERSON>er le Premier Admin - Guide Postman

## 🚀 Étapes pour créer votre premier administrateur

### **Étape 1: Migration Propre**
```bash
php artisan migrate:fresh --seed
```

### **Étape 2: <PERSON><PERSON><PERSON> le Premier Admin via Postman**

#### **2.1. Inscription du Premier Admin**
**Méthode :** POST  
**URL :** `http://127.0.0.1:8000/api/auth/register`

**Headers :**
```
Content-Type: application/json
Accept: application/json
```

**Body (JSON) :**
```json
{
  "first_name": "Admin",
  "last_name": "EcoEvents",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "phone": "+33123456789",
  "city": "Paris",
  "country": "France",
  "bio": "Administrateur principal de la plateforme EcoEvents"
}
```

**Résultat attendu :** Status 201 - Utilisateur créé avec token

#### **2.2. Connexion pour récupérer le token**
**Méthode :** POST  
**URL :** `http://127.0.0.1:8000/api/auth/login`

**Headers :**
```
Content-Type: application/json
Accept: application/json
```

**Body (JSON) :**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Résultat attendu :** Status 200 - Token admin récupéré

#### **2.3. Changer le rôle en Admin (via base de données)**
Puisque nous n'avons pas encore d'admin, vous devez modifier directement la base de données :

**Option A: Via Tinker (Recommandé)**
```bash
php artisan tinker
```

```php
$user = App\Models\User::where('email', '<EMAIL>')->first();
$user->role = 'admin';
$user->is_verified = true;
$user->save();
exit
```

**Option B: Via SQLite Browser**
Ouvrez le fichier `database/database.sqlite` et modifiez :
- `role` : `user` → `admin`
- `is_verified` : `0` → `1`

### **Étape 3: Vérification Admin**

#### **3.1. Test des permissions admin**
**Méthode :** GET  
**URL :** `http://127.0.0.1:8000/api/admin/users/stats`

**Headers :**
```
Accept: application/json
Authorization: Bearer VOTRE_TOKEN_ADMIN
```

**Résultat attendu :** Status 200 - Statistiques des utilisateurs

### **Étape 4: Créer d'autres utilisateurs via API**

#### **4.1. Créer un Organizer (en tant qu'Admin)**
**Méthode :** POST  
**URL :** `http://127.0.0.1:8000/api/users`

**Headers :**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer VOTRE_TOKEN_ADMIN
```

**Body (JSON) :**
```json
{
  "first_name": "Marie",
  "last_name": "Dubois",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "role": "organizer",
  "phone": "+33987654321",
  "city": "Lyon",
  "country": "France",
  "bio": "Organisatrice d'événements écologiques"
}
```

#### **4.2. Créer un User normal (en tant qu'Admin)**
**Méthode :** POST  
**URL :** `http://127.0.0.1:8000/api/users`

**Headers :**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer VOTRE_TOKEN_ADMIN
```

**Body (JSON) :**
```json
{
  "first_name": "Pierre",
  "last_name": "Dupont",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "role": "user",
  "phone": "+33666777888",
  "city": "Nantes",
  "country": "France",
  "bio": "Utilisateur passionné d'écologie"
}
```

## 📋 Comptes de Test à Créer

| Rôle | Email | Mot de passe | Description |
|------|-------|--------------|-------------|
| 👑 **Admin** | <EMAIL> | password123 | Admin principal |
| 🎯 **Organizer** | <EMAIL> | password123 | Organizer test |
| 👤 **User** | <EMAIL> | password123 | User test |

## ✅ Checklist de Vérification

- [ ] Migration exécutée avec succès
- [ ] Premier admin créé via POST /auth/register
- [ ] Admin connecté et token récupéré
- [ ] Rôle admin assigné via Tinker/SQL
- [ ] Permissions admin testées
- [ ] Autres utilisateurs créés via API

## 🚨 Points Importants

1. **Premier Admin** : Doit être créé via inscription puis modifié en base
2. **Tokens** : Sauvegardez les tokens pour les tests suivants
3. **Rôles** : Utilisez les valeurs exactes : `admin`, `organizer`, `user`
4. **Validation** : `first_name` et `last_name` sont maintenant obligatoires

---

**🎯 Vous avez maintenant une base de données propre et pouvez créer tous vos utilisateurs via l'API !**


