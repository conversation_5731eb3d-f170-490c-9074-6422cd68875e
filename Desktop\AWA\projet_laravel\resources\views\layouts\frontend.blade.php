<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'EcoEvents - Plateforme d\'événements écologiques')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        :root {
            --eco-green: #22c55e;
            --eco-dark-green: #16a34a;
            --eco-light-green: #dcfce7;
            --eco-blue: #3b82f6;
            --eco-dark: #1f2937;
        }

        body {
            font-family: 'Figtree', sans-serif;
            background-color: #f8fafc;
        }

        .navbar-brand {
            font-weight: 600;
            color: var(--eco-green) !important;
        }

        .btn-eco {
            background-color: var(--eco-green);
            border-color: var(--eco-green);
            color: white;
        }

        .btn-eco:hover {
            background-color: var(--eco-dark-green);
            border-color: var(--eco-dark-green);
            color: white;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--eco-green) 0%, var(--eco-blue) 100%);
            color: white;
            padding: 100px 0;
        }

        .card-event {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card-event:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .footer {
            background-color: var(--eco-dark);
            color: white;
            padding: 50px 0 20px 0;
        }

        .eco-badge {
            background-color: var(--eco-light-green);
            color: var(--eco-dark-green);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
    </style>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @stack('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <i class="fas fa-leaf me-2"></i>EcoEvents
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('home') }}">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('events.index') }}">Événements</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('categories.index') }}">Catégories</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('about') }}">À propos</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    @guest
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('login') }}">Connexion</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-eco ms-2" href="{{ route('register') }}">Inscription</a>
                        </li>
                    @else
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ Auth::user()->first_name }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('profile') }}">Mon Profil</a></li>
                                <li><a class="dropdown-item" href="{{ route('my-events') }}">Mes Événements</a></li>
                                @if(Auth::user()->isAdmin() || Auth::user()->isOrganizer())
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">Administration</a></li>
                                @endif
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">Déconnexion</button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    @endguest
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show m-0" role="alert">
            <div class="container">
                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show m-0" role="alert">
            <div class="container">
                <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    @endif

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><i class="fas fa-leaf me-2"></i>EcoEvents</h5>
                    <p class="text-light">Plateforme dédiée aux événements écologiques et au développement durable. Ensemble, créons un avenir plus vert.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Navigation</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ route('home') }}" class="text-light text-decoration-none">Accueil</a></li>
                        <li><a href="{{ route('events.index') }}" class="text-light text-decoration-none">Événements</a></li>
                        <li><a href="{{ route('categories.index') }}" class="text-light text-decoration-none">Catégories</a></li>
                        <li><a href="{{ route('about') }}" class="text-light text-decoration-none">À propos</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Compte</h6>
                    <ul class="list-unstyled">
                        @guest
                            <li><a href="{{ route('login') }}" class="text-light text-decoration-none">Connexion</a></li>
                            <li><a href="{{ route('register') }}" class="text-light text-decoration-none">Inscription</a></li>
                        @else
                            <li><a href="{{ route('profile') }}" class="text-light text-decoration-none">Mon Profil</a></li>
                            <li><a href="{{ route('my-events') }}" class="text-light text-decoration-none">Mes Événements</a></li>
                        @endguest
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6>Newsletter</h6>
                    <p class="text-light">Restez informé des derniers événements écologiques.</p>
                    <form class="d-flex">
                        <input type="email" class="form-control me-2" placeholder="Votre email">
                        <button class="btn btn-eco" type="submit">S'abonner</button>
                    </form>
                </div>
            </div>
            <hr class="text-light">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-light mb-0">&copy; {{ date('Y') }} EcoEvents. Tous droits réservés.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-light text-decoration-none me-3">Politique de confidentialité</a>
                    <a href="#" class="text-light text-decoration-none">Conditions d'utilisation</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    @stack('scripts')
</body>
</html>
