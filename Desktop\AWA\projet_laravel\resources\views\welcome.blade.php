@extends('layouts.frontend')

@section('title', 'EcoEvents - Plateforme d\'événements écologiques')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    Ensemble pour un avenir <span class="text-warning">durable</span>
                </h1>
                <p class="lead mb-4">
                    Découvrez et participez aux événements écologiques près de chez vous.
                    Rejoignez une communauté engagée pour la protection de l'environnement.
                </p>
                <div class="d-flex gap-3">
                    <a href="{{ route('events.index') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-calendar-alt me-2"></i>Voir les événements
                    </a>
                    <a href="{{ route('register') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Rejoindre
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-globe-americas" style="font-size: 15rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-5 bg-white">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="p-4">
                    <i class="fas fa-calendar-check text-success mb-3" style="font-size: 3rem;"></i>
                    <h3 class="fw-bold">{{ $stats['events'] ?? 0 }}</h3>
                    <p class="text-muted">Événements organisés</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-4">
                    <i class="fas fa-users text-primary mb-3" style="font-size: 3rem;"></i>
                    <h3 class="fw-bold">{{ $stats['users'] ?? 0 }}</h3>
                    <p class="text-muted">Membres actifs</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-4">
                    <i class="fas fa-tree text-success mb-3" style="font-size: 3rem;"></i>
                    <h3 class="fw-bold">{{ $stats['trees_planted'] ?? 0 }}</h3>
                    <p class="text-muted">Arbres plantés</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-4">
                    <i class="fas fa-recycle text-warning mb-3" style="font-size: 3rem;"></i>
                    <h3 class="fw-bold">{{ $stats['waste_collected'] ?? 0 }}kg</h3>
                    <p class="text-muted">Déchets collectés</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Events -->
<section class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-3">Événements à la une</h2>
                <p class="lead text-muted">
                    Découvrez les prochains événements écologiques organisés par notre communauté
                </p>
            </div>
        </div>

        <div class="row">
            @forelse($featuredEvents ?? [] as $event)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card card-event h-100">
                        @if($event->image)
                            <img src="{{ $event->image }}" class="card-img-top" alt="{{ $event->title }}" style="height: 200px; object-fit: cover;">
                        @else
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="fas fa-image text-muted" style="font-size: 3rem;"></i>
                            </div>
                        @endif

                        <div class="card-body d-flex flex-column">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="eco-badge">{{ $event->category->name ?? 'Général' }}</span>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ $event->date ? $event->date->format('d/m/Y') : 'Date à définir' }}
                                </small>
                            </div>

                            <h5 class="card-title">{{ $event->title ?? 'Titre de l\'événement' }}</h5>
                            <p class="card-text text-muted flex-grow-1">
                                {{ Str::limit($event->description ?? 'Description de l\'événement...', 100) }}
                            </p>

                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ $event->location ?? 'Lieu à définir' }}
                                </small>
                                <a href="{{ route('events.show', $event->id ?? '#') }}" class="btn btn-eco btn-sm">
                                    En savoir plus
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <div class="py-5">
                        <i class="fas fa-calendar-times text-muted mb-3" style="font-size: 4rem;"></i>
                        <h4 class="text-muted">Aucun événement à la une</h4>
                        <p class="text-muted">Les prochains événements seront bientôt disponibles.</p>
                        <a href="{{ route('events.index') }}" class="btn btn-eco">
                            Voir tous les événements
                        </a>
                    </div>
                </div>
            @endforelse
        </div>

        @if(count($featuredEvents ?? []) > 0)
            <div class="text-center mt-4">
                <a href="{{ route('events.index') }}" class="btn btn-outline-primary btn-lg">
                    Voir tous les événements
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Categories Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-3">Catégories d'événements</h2>
                <p class="lead text-muted">
                    Explorez les différents types d'événements écologiques
                </p>
            </div>
        </div>

        <div class="row">
            @php
                $defaultCategories = [
                    ['name' => 'Nettoyage', 'icon' => 'fas fa-broom', 'color' => 'success'],
                    ['name' => 'Plantation', 'icon' => 'fas fa-seedling', 'color' => 'primary'],
                    ['name' => 'Sensibilisation', 'icon' => 'fas fa-bullhorn', 'color' => 'warning'],
                    ['name' => 'Recyclage', 'icon' => 'fas fa-recycle', 'color' => 'info'],
                    ['name' => 'Biodiversité', 'icon' => 'fas fa-leaf', 'color' => 'success'],
                    ['name' => 'Énergie verte', 'icon' => 'fas fa-solar-panel', 'color' => 'warning']
                ];
            @endphp

            @foreach($categories ?? $defaultCategories as $category)
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <div class="text-center">
                        <div class="bg-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center"
                             style="width: 80px; height: 80px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <i class="{{ $category['icon'] ?? 'fas fa-calendar' }} text-{{ $category['color'] ?? 'primary' }}"
                               style="font-size: 2rem;"></i>
                        </div>
                        <h6 class="fw-bold">{{ $category['name'] ?? 'Catégorie' }}</h6>
                        <small class="text-muted">{{ $category['events_count'] ?? rand(5, 25) }} événements</small>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="text-center mt-4">
            <a href="{{ route('categories.index') }}" class="btn btn-outline-primary">
                Voir toutes les catégories
            </a>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="fw-bold mb-2">Prêt à faire la différence ?</h3>
                <p class="mb-0">Rejoignez notre communauté et participez aux événements écologiques près de chez vous.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                @guest
                    <a href="{{ route('register') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>S'inscrire maintenant
                    </a>
                @else
                    <a href="{{ route('events.index') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-calendar-alt me-2"></i>Découvrir les événements
                    </a>
                @endguest
            </div>
        </div>
    </div>
</section>
@endsection
