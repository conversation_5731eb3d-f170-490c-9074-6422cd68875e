# 🚀 Guide de Démarrage Rapide - EcoEvents API

## ⚡ Démarrage Express (5 minutes)

### 1. Configuration Initiale
```bash
cd projet_laravel

# Installer les dépendances
composer install

# Configuration de l'environnement
cp .env.example .env
php artisan key:generate

# Configuration de la base de données (modifiez .env si nécessaire)
# DB_CONNECTION=sqlite
# DB_DATABASE=/path/to/database.sqlite
# OU utilisez MySQL/PostgreSQL

# Créer la base de données et exécuter les migrations
php artisan migrate

# Charger les données de test
php artisan db:seed
```

### 2. D<PERSON>marrer le Serveur
```bash
php artisan serve
```
**✅ Serveur démarré sur : http://localhost:8000**

### 3. Test Rapide avec cURL

#### Test 1: Leaderboard (Public)
```bash
curl http://localhost:8000/api/users/leaderboard
```

#### Test 2: Connexion Admin
```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

#### Test 3: Profil (avec le token reçu)
```bash
curl http://localhost:8000/api/profile \
  -H "Authorization: Bearer VOTRE_TOKEN_ICI"
```

## 🧪 Tests Automatisés

### Option 1: Script de Test (Recommandé)
```bash
# Rendre le script exécutable
chmod +x test_api.sh

# Exécuter tous les tests
./test_api.sh
```

### Option 2: Tests Manuels
Suivez le guide détaillé dans `TEST_GUIDE.md`

## 📊 Comptes de Test

| Rôle | Email | Mot de passe | Permissions |
|------|-------|--------------|-------------|
| 👑 **Admin** | <EMAIL> | password123 | Accès complet |
| 🎯 **Organizer** | <EMAIL> | password123 | Gestion users |
| 👤 **User** | <EMAIL> | password123 | Profil personnel |

## 🔧 Outils Recommandés

### Pour les Tests API
- **Postman** - Interface graphique
- **Insomnia** - Alternative à Postman
- **Thunder Client** - Extension VS Code

### Pour le Développement
- **VS Code** + Extensions PHP/Laravel
- **PHPStorm** - IDE professionnel
- **Laravel Debugbar** - Debug en temps réel

## 📋 Checklist de Vérification

- [ ] ✅ Serveur démarré (php artisan serve)
- [ ] ✅ Base de données configurée
- [ ] ✅ Migrations exécutées
- [ ] ✅ Seeders exécutés
- [ ] ✅ Test leaderboard fonctionne
- [ ] ✅ Connexion admin fonctionne
- [ ] ✅ Token reçu et valide
- [ ] ✅ Profil utilisateur accessible

## 🐛 Problèmes Courants

### Erreur: "Class not found"
```bash
composer dump-autoload
```

### Erreur: "Database connection"
Vérifiez votre fichier `.env` :
```env
DB_CONNECTION=sqlite
DB_DATABASE=/path/to/database.sqlite
```

### Erreur: "Token invalid"
- Vérifiez que vous utilisez le bon token
- Vérifiez le format : `Bearer TOKEN`

### Erreur: "Route not found"
- Vérifiez que le serveur est démarré
- Vérifiez l'URL : `http://localhost:8000/api/...`

## 📈 Prochaines Étapes

1. **Explorer l'API** - Testez toutes les routes
2. **Créer un Frontend** - React/Vue.js/Angular
3. **Ajouter des Fonctionnalités** - Events, Bookings, etc.
4. **Déployer** - Heroku, Vercel, AWS

## 🆘 Support

- 📖 **Documentation Laravel** : https://laravel.com/docs
- 📚 **Guide de Test** : `TEST_GUIDE.md`
- 🧪 **Script de Test** : `test_api.sh`
- 📝 **Logs** : `storage/logs/laravel.log`

---

**🎉 Vous êtes prêt à tester votre API EcoEvents !**

