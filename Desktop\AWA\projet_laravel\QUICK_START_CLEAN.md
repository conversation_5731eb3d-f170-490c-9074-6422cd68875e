# 🚀 Démarrage Rapide - Base de Données Propre

## ⚡ Configuration en 3 Étapes

### **1. Migration Propre**
```bash
cd projet_laravel
php artisan migrate:fresh --seed
```

**✅ Résultat :** Base de données vide avec seulement les tables

### **2. Premier Admin via Postman**

#### **Inscription :**
```
POST http://127.0.0.1:8000/api/auth/register
Content-Type: application/json

{
  "first_name": "Admin",
  "last_name": "EcoEvents",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123"
}
```

#### **Promouvoir en Admin :**
```bash
php artisan tinker
```
```php
$user = App\Models\User::where('email', '<EMAIL>')->first();
$user->role = 'admin';
$user->is_verified = true;
$user->save();
exit
```

### **3. Test des Permissions**
```
GET http://127.0.0.1:8000/api/admin/users/stats
Authorization: Bearer VOTRE_TOKEN
```

## 🎯 Avantages de cette Approche

- ✅ **Base propre** - Pas de données de test
- ✅ **Contrôle total** - Vous créez tous les utilisateurs
- ✅ **Tests réalistes** - Comme en production
- ✅ **Flexibilité** - Créez les utilisateurs selon vos besoins

## 📋 Prochaines Étapes

1. **Créer des utilisateurs** via l'API
2. **Tester les rôles** et permissions
3. **Développer les fonctionnalités** suivantes

---

**🎉 Votre API est prête avec une base de données propre !**


