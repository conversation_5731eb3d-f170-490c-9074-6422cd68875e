<?php

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Only admins and organizers can view the user list
        return $user->isAdmin() || $user->isOrganizer();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        // Users can view their own profile, admins can view anyone
        return $user->id === $model->id || $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Only admins and organizers can create users
        return $user->isAdmin() || $user->isOrganizer();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        // Users can update their own profile
        if ($user->id === $model->id) {
            return true;
        }

        // Admins can update anyone
        if ($user->isAdmin()) {
            return true;
        }

        // Organizers can update users but not admins
        if ($user->isOrganizer()) {
            return !$model->isAdmin();
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        // Users cannot delete themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Only admins can delete users
        if (!$user->isAdmin()) {
            return false;
        }

        // Admins cannot delete other admins
        if ($model->isAdmin()) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        // Only admins can restore users
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        // Only admins can force delete users
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can change the role of the model.
     */
    public function changeRole(User $user, User $model): bool
    {
        // Only admins can change roles
        if (!$user->isAdmin()) {
            return false;
        }

        // Admins cannot change their own role
        if ($user->id === $model->id) {
            return false;
        }

        // Admins cannot change other admin roles
        if ($model->isAdmin()) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can toggle the status of the model.
     */
    public function toggleStatus(User $user, User $model): bool
    {
        // Only admins can toggle status
        if (!$user->isAdmin()) {
            return false;
        }

        // Admins cannot toggle their own status
        if ($user->id === $model->id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can add eco score to the model.
     */
    public function addEcoScore(User $user, User $model): bool
    {
        // Admins and organizers can add eco score to any user
        return $user->isAdmin() || $user->isOrganizer();
    }

    /**
     * Determine whether the user can view the model's profile.
     */
    public function viewProfile(User $user, User $model): bool
    {
        // Users can view their own profile
        if ($user->id === $model->id) {
            return true;
        }

        // Public profiles can be viewed by anyone
        // This could be extended with privacy settings
        return true;
    }

    /**
     * Determine whether the user can update their own profile.
     */
    public function updateProfile(User $user): bool
    {
        // Any authenticated user can update their own profile
        return true;
    }

    /**
     * Determine whether the user can change their password.
     */
    public function changePassword(User $user): bool
    {
        // Any authenticated user can change their password
        return true;
    }

    /**
     * Determine whether the user can upload an avatar.
     */
    public function uploadAvatar(User $user, User $model): bool
    {
        // Users can upload their own avatar
        if ($user->id === $model->id) {
            return true;
        }

        // Admins can upload avatar for any user
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view user statistics.
     */
    public function viewStats(User $user): bool
    {
        // Only admins can view user statistics
        return $user->isAdmin();
    }
}

